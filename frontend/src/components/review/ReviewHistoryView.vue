<template>
  <div class="review-history-view">
    <div class="history-header">
      <div class="header-title">
        <h3>评审历史记录</h3>
        <el-badge :value="totalRecords" type="info" />
      </div>
      
      <div class="header-actions">
        <el-select v-model="filterByItem" placeholder="按检查项筛选" clearable style="width: 200px">
          <el-option
            v-for="item in reviewItems"
            :key="item.itemId"
            :label="`${item.sequence}. ${item.content}`"
            :value="item.itemId"
          />
        </el-select>
        
        <el-select v-model="filterByReviewer" placeholder="按评审人筛选" clearable style="width: 150px">
          <el-option
            v-for="reviewer in reviewers"
            :key="reviewer"
            :label="reviewer"
            :value="reviewer"
          />
        </el-select>
        
        <el-button @click="exportHistory" :disabled="filteredHistory.length === 0">
          <el-icon><Download /></el-icon>
          导出历史
        </el-button>
      </div>
    </div>

    <div class="history-content">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>
      
      <div v-else-if="filteredHistory.length === 0" class="empty-state">
        <div class="empty-icon">📝</div>
        <div class="empty-text">暂无评审历史记录</div>
        <div class="empty-hint">开始评审后，这里将显示所有的评审记录</div>
      </div>
      
      <div v-else class="history-timeline">
        <el-timeline>
          <el-timeline-item
            v-for="record in filteredHistory"
            :key="`${record.itemId}-${record.reviewTime}`"
            :timestamp="formatTime(record.reviewTime)"
            :type="getTimelineType(record.status)"
            :icon="getStatusIcon(record.status)"
            placement="top"
          >
            <div class="history-record">
              <div class="record-header">
                <div class="record-title">
                  <span class="item-sequence">{{ getItemSequence(record.itemId) }}.</span>
                  <span class="item-content">{{ getItemContent(record.itemId) }}</span>
                </div>
                <div class="record-meta">
                  <el-tag :type="getStatusTagType(record.status)" size="small">
                    {{ getStatusText(record.status) }}
                  </el-tag>
                  <span class="reviewer">{{ record.reviewer }}</span>
                </div>
              </div>
              
              <div v-if="record.comment" class="record-comment">
                <div class="comment-label">评审意见：</div>
                <div class="comment-content">{{ record.comment }}</div>
              </div>
              
              <div class="record-category">
                <el-tag type="info" size="small">{{ getItemCategory(record.itemId) }}</el-tag>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Download } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { type ExtendedReviewItem } from '@/types/table-config'
import { ReviewItemStatus } from '@/api/review'

// Props
interface Props {
  reviewId: string
  reviewItems: ExtendedReviewItem[]
}

const props = withDefaults(defineProps<Props>(), {
  reviewItems: () => []
})

// Reactive data
const loading = ref(false)
const filterByItem = ref('')
const filterByReviewer = ref('')

// Computed
const allHistory = computed(() => {
  const history: any[] = []
  
  props.reviewItems.forEach(item => {
    if (item.reviewHistory && item.reviewHistory.length > 0) {
      item.reviewHistory.forEach(record => {
        history.push({
          ...record,
          itemId: item.itemId,
          itemSequence: item.sequence,
          itemContent: item.content,
          itemCategory: item.category
        })
      })
    }
  })
  
  // 按时间倒序排序
  return history.sort((a, b) => {
    const timeA = new Date(a.reviewTime).getTime()
    const timeB = new Date(b.reviewTime).getTime()
    return timeB - timeA
  })
})

const filteredHistory = computed(() => {
  let filtered = allHistory.value
  
  if (filterByItem.value) {
    filtered = filtered.filter(record => record.itemId === filterByItem.value)
  }
  
  if (filterByReviewer.value) {
    filtered = filtered.filter(record => record.reviewer === filterByReviewer.value)
  }
  
  return filtered
})

const totalRecords = computed(() => allHistory.value.length)

const reviewers = computed(() => {
  const reviewerSet = new Set<string>()
  allHistory.value.forEach(record => {
    if (record.reviewer) {
      reviewerSet.add(record.reviewer)
    }
  })
  return Array.from(reviewerSet)
})

// Methods
const formatTime = (time: string) => {
  if (!time) return ''
  const date = new Date(time)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getTimelineType = (status: ReviewItemStatus) => {
  const typeMap = {
    [ReviewItemStatus.PASS]: 'success',
    [ReviewItemStatus.FAIL]: 'danger',
    [ReviewItemStatus.SKIP]: 'warning',
    [ReviewItemStatus.PENDING]: 'info'
  }
  return typeMap[status] || 'info'
}

const getStatusIcon = (status: ReviewItemStatus) => {
  const iconMap = {
    [ReviewItemStatus.PASS]: 'CircleCheck',
    [ReviewItemStatus.FAIL]: 'CircleClose',
    [ReviewItemStatus.SKIP]: 'Remove',
    [ReviewItemStatus.PENDING]: 'Clock'
  }
  return iconMap[status] || 'Clock'
}

const getStatusTagType = (status: ReviewItemStatus) => {
  const typeMap = {
    [ReviewItemStatus.PASS]: 'success',
    [ReviewItemStatus.FAIL]: 'danger',
    [ReviewItemStatus.SKIP]: 'warning',
    [ReviewItemStatus.PENDING]: 'info'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: ReviewItemStatus) => {
  const textMap = {
    [ReviewItemStatus.PASS]: '通过',
    [ReviewItemStatus.FAIL]: '不通过',
    [ReviewItemStatus.SKIP]: '跳过',
    [ReviewItemStatus.PENDING]: '待处理'
  }
  return textMap[status] || status
}

const getItemSequence = (itemId: string) => {
  const item = props.reviewItems.find(item => item.itemId === itemId)
  return item?.sequence || 0
}

const getItemContent = (itemId: string) => {
  const item = props.reviewItems.find(item => item.itemId === itemId)
  return item?.content || ''
}

const getItemCategory = (itemId: string) => {
  const item = props.reviewItems.find(item => item.itemId === itemId)
  return item?.category || ''
}

const exportHistory = () => {
  const exportData = filteredHistory.value.map(record => ({
    检查项序号: record.itemSequence,
    检查内容: record.itemContent,
    分类: record.itemCategory,
    评审状态: getStatusText(record.status),
    评审人: record.reviewer,
    评审时间: formatTime(record.reviewTime),
    评审意见: record.comment || ''
  }))
  
  // 这里应该实现实际的导出逻辑
  console.log('Export history data:', exportData)
  ElMessage.success(`导出 ${exportData.length} 条历史记录`)
}

onMounted(() => {
  // 组件挂载时可以执行一些初始化操作
})
</script>

<style scoped>
.review-history-view {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.history-content {
  flex: 1;
  overflow: auto;
}

.loading-container {
  padding: 20px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #606266;
}

.empty-hint {
  font-size: 14px;
  color: #909399;
}

.history-timeline {
  padding: 20px;
}

.history-record {
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 8px;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.record-title {
  flex: 1;
  margin-right: 16px;
}

.item-sequence {
  font-weight: 600;
  color: #409eff;
  margin-right: 8px;
}

.item-content {
  color: #303133;
  font-size: 14px;
}

.record-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.reviewer {
  font-size: 12px;
  color: #909399;
}

.record-comment {
  margin-bottom: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.comment-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.comment-content {
  font-size: 14px;
  color: #303133;
  line-height: 1.5;
}

.record-category {
  display: flex;
  justify-content: flex-end;
}
</style>
