<template>
  <div class="dynamic-completion-buttons">
    <el-button
      v-for="button in visibleButtons"
      :key="button.id"
      :type="button.type"
      :loading="loadingButtons.has(button.id)"
      :disabled="disabled || !button.enabled"
      @click="handleButtonClick(button)"
      class="completion-button"
      :class="`completion-${button.id}`"
    >
      <el-icon v-if="button.icon">
        <component :is="button.icon" />
      </el-icon>
      {{ button.label }}
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { type CompletionButtonGroup, type CompletionButton } from '@/types/defect-config'
import { type ReviewItem } from '@/api/review'

// Props
interface Props {
  buttonGroup: CompletionButtonGroup
  reviewItems: ReviewItem[]
  currentUser?: string
  loading?: boolean
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  currentUser: 'current-user',
  loading: false,
  disabled: false
})

// Emits
interface Emits {
  (e: 'completion-action', button: CompletionButton, payload: any): void
}

const emit = defineEmits<Emits>()

// Reactive data
const loadingButtons = ref(new Set<string>())

// Computed
const visibleButtons = computed(() => {
  return props.buttonGroup.buttons
    .filter(button => {
      if (!button.enabled) return false
      
      // 执行显示条件脚本
      try {
        return evaluateDisplayCondition(button.displayCondition.script)
      } catch (error) {
        console.error(`Error evaluating display condition for button ${button.id}:`, error)
        return false
      }
    })
    .sort((a, b) => a.order - b.order)
})

// Methods
const evaluateDisplayCondition = (script: string): boolean => {
  try {
    // 创建脚本执行上下文
    const context = {
      reviewItems: props.reviewItems,
      currentUser: props.currentUser,
      // 添加一些辅助函数
      filter: Array.prototype.filter,
      map: Array.prototype.map,
      reduce: Array.prototype.reduce,
      find: Array.prototype.find,
      some: Array.prototype.some,
      every: Array.prototype.every
    }
    
    // 创建安全的函数执行环境
    const func = new Function('reviewItems', 'currentUser', 'filter', 'map', 'reduce', 'find', 'some', 'every', `
      try {
        ${script}
      } catch (error) {
        console.error('Script execution error:', error);
        return false;
      }
    `)
    
    // 执行脚本，设置超时
    const timeoutId = setTimeout(() => {
      throw new Error('Script execution timeout')
    }, props.buttonGroup.scriptContext.timeout || 5000)
    
    const result = func(
      context.reviewItems,
      context.currentUser,
      context.filter,
      context.map,
      context.reduce,
      context.find,
      context.some,
      context.every
    )
    
    clearTimeout(timeoutId)
    return Boolean(result)
  } catch (error) {
    console.error('Failed to evaluate display condition:', error)
    return false
  }
}

const handleButtonClick = async (button: CompletionButton) => {
  try {
    // 显示确认对话框
    if (button.action.confirmMessage) {
      await ElMessageBox.confirm(
        button.action.confirmMessage,
        '确认操作',
        { type: 'warning' }
      )
    }
    
    loadingButtons.value.add(button.id)
    
    // 构建请求载荷
    const context = {
      currentUser: props.currentUser,
      currentTime: new Date().toISOString(),
      reviewItems: props.reviewItems
    }
    
    const payload = buildPayload(button.action.payloadTemplate, context)
    
    // 发送完成评审事件
    emit('completion-action', button, payload)
    
    // 显示成功消息
    if (button.action.successMessage) {
      ElMessage.success(button.action.successMessage)
    }
    
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '操作失败')
    }
  } finally {
    loadingButtons.value.delete(button.id)
  }
}

const buildPayload = (template: Record<string, any>, context: Record<string, any>) => {
  const payload = { ...template }
  
  // 替换模板变量
  const templateStr = JSON.stringify(payload)
  const replacedStr = replaceTemplateVariables(templateStr, context)
  
  return JSON.parse(replacedStr)
}

const replaceTemplateVariables = (template: string, context: Record<string, any>): string => {
  let result = template
  
  // 替换上下文变量
  Object.keys(context).forEach(key => {
    const regex = new RegExp(`\\$\\{${key}\\}`, 'g')
    result = result.replace(regex, String(context[key]))
  })
  
  return result
}
</script>

<style scoped>
.dynamic-completion-buttons {
  display: flex;
  gap: 12px;
}

.completion-button {
  padding: 10px 20px;
  font-weight: 500;
  border-radius: 6px;
}

.completion-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
