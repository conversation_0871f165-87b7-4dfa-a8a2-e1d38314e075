<template>
  <div class="view-mode-selector">
    <div class="selector-content">
      <!-- 视图模式切换 -->
      <div class="view-modes">
        <el-radio-group v-model="currentMode" @change="handleModeChange">
          <el-radio-button value="card">
            <el-icon><Grid /></el-icon>
            卡片视图
          </el-radio-button>
          <el-radio-button value="table">
            <el-icon><List /></el-icon>
            表格视图
          </el-radio-button>
        </el-radio-group>
      </div>

      <!-- 表格视图专用控制 -->
      <div v-if="currentMode === 'table'" class="table-controls">
        <!-- 配置方案选择 -->
        <div class="config-selector">
          <el-select
            :model-value="currentConfigId"
            @update:model-value="handleConfigChange"
            placeholder="选择配置方案"
            style="width: 180px"
          >
            <el-option
              v-for="config in tableConfigs"
              :key="config.id"
              :label="config.name"
              :value="config.id"
            />
          </el-select>
        </div>

        <!-- 快速操作按钮 -->
        <div class="quick-actions">
          <el-button-group>
            <el-button
              @click="toggleGroupMode"
              :type="groupMode ? 'primary' : ''"
              size="small"
            >
              <el-icon><FolderOpened /></el-icon>
              {{ groupMode ? '取消分组' : '启用分组' }}
            </el-button>
          </el-button-group>
        </div>

        <!-- 分组选择器 -->
        <div v-if="groupMode" class="group-selector">
          <el-select
            v-model="groupByField"
            @change="handleGroupByChange"
            placeholder="选择分组字段"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="field in groupableFields"
              :key="field.key"
              :label="field.label"
              :value="field.key"
            />
          </el-select>
        </div>
      </div>

      <!-- 通用控制 -->
      <div class="common-controls">
        <!-- 搜索框 -->
        <div class="search-box">
          <el-input
            :model-value="searchText"
            @update:model-value="handleSearchChange"
            placeholder="搜索检查项..."
            clearable
            style="width: 250px"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <!-- 状态筛选 -->
        <div class="status-filter">
          <el-select
            :model-value="statusFilter"
            @update:model-value="handleStatusFilterChange"
            placeholder="状态筛选"
            clearable
            style="width: 140px"
          >
            <el-option
              v-for="option in statusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </div>

        <!-- 更多筛选 -->
        <el-dropdown @command="handleFilterCommand">
          <el-button size="small">
            更多筛选
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="required">仅显示必填项</el-dropdown-item>
              <el-dropdown-item command="commented">仅显示有备注项</el-dropdown-item>
              <el-dropdown-item command="recent">最近评审</el-dropdown-item>
              <el-dropdown-item divided command="clear">清除所有筛选</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import {
  Grid, List, FolderOpened, Search, ArrowDown
} from '@element-plus/icons-vue'
import {
  type TableViewConfig,
  ViewMode,
  DEFAULT_TABLE_CONFIG
} from '@/types/table-config'

// Props
interface Props {
  mode: ViewMode
  tableConfigs: TableViewConfig[]
  currentConfigId: string
  searchText: string
  statusFilter: string
  buttonConfig?: any // 按钮配置，用于获取状态选项
}

const props = withDefaults(defineProps<Props>(), {
  mode: ViewMode.CARD,
  tableConfigs: () => [DEFAULT_TABLE_CONFIG],
  currentConfigId: 'default',
  searchText: '',
  statusFilter: ''
})

// Emits
interface Emits {
  (e: 'mode-change', mode: ViewMode): void
  (e: 'config-change', configId: string): void
  (e: 'search-change', text: string): void
  (e: 'status-filter-change', status: string): void
  (e: 'group-by-change', field: string): void
  (e: 'filter-command', command: string): void
}

const emit = defineEmits<Emits>()

// Reactive data
const currentMode = ref<ViewMode>(props.mode)
const groupMode = ref(false)
const groupByField = ref('')
const searchText = ref(props.searchText)
const statusFilter = ref(props.statusFilter)

// Computed
const currentTableConfig = computed(() => {
  return props.tableConfigs.find(config => config.id === props.currentConfigId)
    || DEFAULT_TABLE_CONFIG
})

// 从按钮组配置中获取状态选项
const statusOptions = computed(() => {
  const options = [{ label: '全部状态', value: '' }]

  if (props.buttonConfig && props.buttonConfig.buttons) {
    props.buttonConfig.buttons.forEach((button: any) => {
      if (button.enabled && button.status) {
        options.push({
          label: `${button.icon || ''} ${button.label}`.trim(),
          value: button.status
        })
      }
    })
  }

  // 如果没有按钮配置，使用默认选项
  if (options.length === 1) {
    options.push(
      { label: '⏳ 待处理', value: 'PENDING' },
      { label: '✅ 通过', value: 'PASS' },
      { label: '❌ 不通过', value: 'FAIL' },
      { label: '⏭️ 跳过', value: 'SKIP' }
    )
  }

  return options
})

const groupableFields = computed(() => {
  const fields = currentTableConfig.value.columns.filter(col =>
    (col.type === 'tag' || col.type === 'text') && col.key !== 'content'
  ).map(col => ({
    key: col.key,
    label: col.label
  }))

  // 将分类字段排在第一位
  return fields.sort((a, b) => {
    if (a.key === 'category') return -1
    if (b.key === 'category') return 1
    return 0
  })
})

// Watch
watch(() => props.mode, (newMode) => {
  currentMode.value = newMode
})

watch(() => props.searchText, (newText) => {
  searchText.value = newText
})

watch(() => props.statusFilter, (newFilter) => {
  statusFilter.value = newFilter
})

watch(() => currentTableConfig.value.groupBy, (groupBy) => {
  if (groupBy) {
    groupMode.value = true
    groupByField.value = groupBy
  }
})

// Methods
const handleModeChange = (mode: ViewMode) => {
  emit('mode-change', mode)
}

const handleConfigChange = (configId: string) => {
  emit('config-change', configId)
}



const handleSearchChange = (text: string) => {
  emit('search-change', text)
}

const handleStatusFilterChange = (status: string) => {
  emit('status-filter-change', status)
}

const toggleGroupMode = () => {
  groupMode.value = !groupMode.value
  if (!groupMode.value) {
    groupByField.value = ''
    emit('group-by-change', '')
  } else if (groupableFields.value.length > 0) {
    // 优先选择分类字段，如果没有则选择第一个可用字段
    const categoryField = groupableFields.value.find(field => field.key === 'category')
    groupByField.value = categoryField ? categoryField.key : groupableFields.value[0].key
    emit('group-by-change', groupByField.value)
  }
}

const handleGroupByChange = (field: string) => {
  emit('group-by-change', field)
}

const handleFilterCommand = (command: string) => {
  emit('filter-command', command)
}
</script>

<style scoped>
.view-mode-selector {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.selector-content {
  display: flex;
  align-items: center;
  gap: 24px;
  flex-wrap: wrap;
}

.view-modes {
  .el-radio-group {
    .el-radio-button {
      .el-radio-button__inner {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 8px 16px;
      }
    }
  }
}

.table-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  padding-left: 24px;
  border-left: 1px solid #e4e7ed;
}

.config-selector {
  .el-select {
    .el-input__inner {
      font-size: 14px;
    }
  }
}

.quick-actions {
  .el-button-group {
    .el-button {
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }
}

.group-selector {
  .el-select {
    .el-input__inner {
      font-size: 14px;
    }
  }
}

.common-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-left: auto;
}

.search-box {
  .el-input {
    .el-input__inner {
      font-size: 14px;
    }
  }
}

.status-filter {
  .el-select {
    .el-input__inner {
      font-size: 14px;
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .selector-content {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .table-controls {
    padding-left: 0;
    border-left: none;
    border-top: 1px solid #e4e7ed;
    padding-top: 16px;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .common-controls {
    margin-left: 0;
    justify-content: flex-start;
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .view-mode-selector {
    padding: 12px;
  }

  .selector-content {
    gap: 12px;
  }

  .table-controls,
  .common-controls {
    gap: 12px;
  }

  .search-box .el-input,
  .status-filter .el-select,
  .config-selector .el-select,
  .group-selector .el-select {
    width: 100% !important;
    min-width: 120px;
  }

  .view-modes .el-radio-group {
    width: 100%;
  }

  .view-modes .el-radio-button {
    flex: 1;
  }
}
</style>
