import request, { type RequestConfig } from '@/utils/request'
import {
  type TemplateSnapshot,
  type ReviewTemplateVersion,
  type VersionComparison,
  type VersionUpgradeOptions,
  type VersionUpgradeResult,
  type TemplateVersionConfig
} from '@/types/template-version'

/**
 * 模板版本管理API
 */
class TemplateVersionApiService {
  private baseUrl = '/template-version'

  /**
   * 创建模板快照
   */
  async createSnapshot(
    templateId: string,
    version: string,
    description?: string,
    config?: RequestConfig
  ): Promise<TemplateSnapshot> {
    if (!templateId?.trim()) {
      throw new Error('模板ID不能为空')
    }
    if (!version?.trim()) {
      throw new Error('版本号不能为空')
    }

    return request.post(`${this.baseUrl}/snapshots`, {
      templateId,
      version,
      description
    }, {
      showLoading: true,
      showSuccess: true,
      successMessage: '模板快照创建成功',
      ...config,
    })
  }

  /**
   * 获取模板的所有快照
   */
  async getTemplateSnapshots(
    templateId: string,
    options?: {
      limit?: number
      offset?: number
      includeContent?: boolean
    },
    config?: RequestConfig
  ): Promise<{
    snapshots: TemplateSnapshot[]
    total: number
    hasMore: boolean
  }> {
    if (!templateId?.trim()) {
      throw new Error('模板ID不能为空')
    }

    return request.get(`${this.baseUrl}/templates/${templateId}/snapshots`, {
      params: options,
      showLoading: false,
      ...config,
    })
  }

  /**
   * 获取特定快照详情
   */
  async getSnapshot(snapshotId: string, config?: RequestConfig): Promise<TemplateSnapshot> {
    if (!snapshotId?.trim()) {
      throw new Error('快照ID不能为空')
    }

    return request.get(`${this.baseUrl}/snapshots/${snapshotId}`, {
      showLoading: false,
      ...config,
    })
  }

  /**
   * 删除快照
   */
  async deleteSnapshot(snapshotId: string, config?: RequestConfig): Promise<void> {
    if (!snapshotId?.trim()) {
      throw new Error('快照ID不能为空')
    }

    return request.delete(`${this.baseUrl}/snapshots/${snapshotId}`, {
      showLoading: true,
      showSuccess: true,
      successMessage: '快照删除成功',
      ...config,
    })
  }

  /**
   * 为检查单创建模板版本关联
   */
  async createReviewTemplateVersion(
    reviewId: string,
    templateId: string,
    snapshotId?: string,
    config?: RequestConfig
  ): Promise<ReviewTemplateVersion> {
    if (!reviewId?.trim()) {
      throw new Error('检查单ID不能为空')
    }
    if (!templateId?.trim()) {
      throw new Error('模板ID不能为空')
    }

    return request.post(`${this.baseUrl}/review-versions`, {
      reviewId,
      templateId,
      snapshotId
    }, {
      showLoading: true,
      ...config,
    })
  }

  /**
   * 获取检查单的模板版本信息
   */
  async getReviewTemplateVersion(
    reviewId: string,
    config?: RequestConfig
  ): Promise<ReviewTemplateVersion> {
    if (!reviewId?.trim()) {
      throw new Error('检查单ID不能为空')
    }

    return request.get(`${this.baseUrl}/reviews/${reviewId}/template-version`, {
      showLoading: false,
      ...config,
    })
  }

  /**
   * 比较两个版本
   */
  async compareVersions(
    currentSnapshotId: string,
    targetSnapshotId: string,
    config?: RequestConfig
  ): Promise<VersionComparison> {
    if (!currentSnapshotId?.trim() || !targetSnapshotId?.trim()) {
      throw new Error('快照ID不能为空')
    }

    return request.post(`${this.baseUrl}/compare`, {
      currentSnapshotId,
      targetSnapshotId
    }, {
      showLoading: true,
      ...config,
    })
  }

  /**
   * 检查检查单是否需要版本升级
   */
  async checkUpgradeAvailable(
    reviewId: string,
    config?: RequestConfig
  ): Promise<{
    upgradeAvailable: boolean
    currentVersion: string
    latestVersion: string
    latestSnapshotId: string
    changes?: VersionComparison
  }> {
    if (!reviewId?.trim()) {
      throw new Error('检查单ID不能为空')
    }

    return request.get(`${this.baseUrl}/reviews/${reviewId}/check-upgrade`, {
      showLoading: false,
      ...config,
    })
  }

  /**
   * 升级检查单到新版本
   */
  async upgradeReviewVersion(
    options: VersionUpgradeOptions,
    config?: RequestConfig
  ): Promise<VersionUpgradeResult> {
    if (!options.reviewId?.trim()) {
      throw new Error('检查单ID不能为空')
    }
    if (!options.targetSnapshotId?.trim()) {
      throw new Error('目标快照ID不能为空')
    }

    return request.post(`${this.baseUrl}/reviews/${options.reviewId}/upgrade`, options, {
      showLoading: true,
      showSuccess: true,
      successMessage: '版本升级成功',
      ...config,
    })
  }

  /**
   * 回滚检查单到指定版本
   */
  async rollbackReviewVersion(
    reviewId: string,
    targetSnapshotId: string,
    options: {
      createBackup: boolean
      backupDescription?: string
    },
    config?: RequestConfig
  ): Promise<VersionUpgradeResult> {
    if (!reviewId?.trim()) {
      throw new Error('检查单ID不能为空')
    }
    if (!targetSnapshotId?.trim()) {
      throw new Error('目标快照ID不能为空')
    }

    return request.post(`${this.baseUrl}/reviews/${reviewId}/rollback`, {
      targetSnapshotId,
      options
    }, {
      showLoading: true,
      showSuccess: true,
      successMessage: '版本回滚成功',
      ...config,
    })
  }

  /**
   * 获取检查单的版本历史
   */
  async getReviewVersionHistory(
    reviewId: string,
    config?: RequestConfig
  ): Promise<Array<{
    snapshotId: string
    version: string
    createdTime: string
    description?: string
    changeType: 'create' | 'upgrade' | 'rollback'
    changes?: {
      itemsAdded: number
      itemsRemoved: number
      itemsModified: number
      configsUpdated: number
    }
  }>> {
    if (!reviewId?.trim()) {
      throw new Error('检查单ID不能为空')
    }

    return request.get(`${this.baseUrl}/reviews/${reviewId}/history`, {
      showLoading: false,
      ...config,
    })
  }

  /**
   * 获取模板版本管理配置
   */
  async getVersionConfig(templateId: string, config?: RequestConfig): Promise<TemplateVersionConfig> {
    if (!templateId?.trim()) {
      throw new Error('模板ID不能为空')
    }

    return request.get(`${this.baseUrl}/templates/${templateId}/config`, {
      showLoading: false,
      ...config,
    })
  }

  /**
   * 更新模板版本管理配置
   */
  async updateVersionConfig(
    templateId: string,
    versionConfig: TemplateVersionConfig,
    config?: RequestConfig
  ): Promise<void> {
    if (!templateId?.trim()) {
      throw new Error('模板ID不能为空')
    }

    return request.put(`${this.baseUrl}/templates/${templateId}/config`, versionConfig, {
      showLoading: true,
      showSuccess: true,
      successMessage: '版本配置更新成功',
      ...config,
    })
  }

  /**
   * 自动创建快照（基于配置规则）
   */
  async autoCreateSnapshot(
    templateId: string,
    changeType: 'template' | 'config' | 'publish',
    config?: RequestConfig
  ): Promise<TemplateSnapshot | null> {
    if (!templateId?.trim()) {
      throw new Error('模板ID不能为空')
    }

    return request.post(`${this.baseUrl}/templates/${templateId}/auto-snapshot`, {
      changeType
    }, {
      showLoading: false,
      ...config,
    })
  }

  /**
   * 清理过期快照
   */
  async cleanupSnapshots(
    templateId: string,
    options?: {
      dryRun?: boolean  // 仅预览，不实际删除
    },
    config?: RequestConfig
  ): Promise<{
    toDelete: Array<{
      snapshotId: string
      version: string
      reason: string
    }>
    deleted?: number
  }> {
    if (!templateId?.trim()) {
      throw new Error('模板ID不能为空')
    }

    return request.post(`${this.baseUrl}/templates/${templateId}/cleanup`, options, {
      showLoading: true,
      ...config,
    })
  }

  /**
   * 导出模板快照
   */
  async exportSnapshot(snapshotId: string, config?: RequestConfig): Promise<Blob> {
    if (!snapshotId?.trim()) {
      throw new Error('快照ID不能为空')
    }

    return request.get(`${this.baseUrl}/snapshots/${snapshotId}/export`, {
      showLoading: true,
      responseType: 'blob',
      ...config,
    }) as Promise<Blob>
  }

  /**
   * 导入模板快照
   */
  async importSnapshot(
    templateId: string,
    snapshotFile: File,
    options: {
      version?: string
      description?: string
      overwriteExisting?: boolean
    },
    config?: RequestConfig
  ): Promise<TemplateSnapshot> {
    if (!templateId?.trim()) {
      throw new Error('模板ID不能为空')
    }
    if (!snapshotFile) {
      throw new Error('快照文件不能为空')
    }

    const formData = new FormData()
    formData.append('snapshot', snapshotFile)
    formData.append('options', JSON.stringify(options))

    return request.post(`${this.baseUrl}/templates/${templateId}/import`, formData, {
      showLoading: true,
      showSuccess: true,
      successMessage: '快照导入成功',
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      ...config,
    })
  }

  /**
   * 获取使用特定快照的检查单列表
   */
  async getSnapshotUsage(
    snapshotId: string,
    config?: RequestConfig
  ): Promise<Array<{
    reviewId: string
    reviewName: string
    status: string
    createdTime: string
    createdBy: string
  }>> {
    if (!snapshotId?.trim()) {
      throw new Error('快照ID不能为空')
    }

    return request.get(`${this.baseUrl}/snapshots/${snapshotId}/usage`, {
      showLoading: false,
      ...config,
    })
  }
}

// Create and export service instance
const templateVersionApi = new TemplateVersionApiService()

// Export individual methods for convenience with proper this binding
export const createSnapshot = templateVersionApi.createSnapshot.bind(templateVersionApi)
export const getTemplateSnapshots = templateVersionApi.getTemplateSnapshots.bind(templateVersionApi)
export const getSnapshot = templateVersionApi.getSnapshot.bind(templateVersionApi)
export const deleteSnapshot = templateVersionApi.deleteSnapshot.bind(templateVersionApi)
export const createReviewTemplateVersion = templateVersionApi.createReviewTemplateVersion.bind(templateVersionApi)
export const getReviewTemplateVersion = templateVersionApi.getReviewTemplateVersion.bind(templateVersionApi)
export const compareVersions = templateVersionApi.compareVersions.bind(templateVersionApi)
export const checkUpgradeAvailable = templateVersionApi.checkUpgradeAvailable.bind(templateVersionApi)
export const upgradeReviewVersion = templateVersionApi.upgradeReviewVersion.bind(templateVersionApi)
export const rollbackReviewVersion = templateVersionApi.rollbackReviewVersion.bind(templateVersionApi)
export const getReviewVersionHistory = templateVersionApi.getReviewVersionHistory.bind(templateVersionApi)
export const getVersionConfig = templateVersionApi.getVersionConfig.bind(templateVersionApi)
export const updateVersionConfig = templateVersionApi.updateVersionConfig.bind(templateVersionApi)
export const autoCreateSnapshot = templateVersionApi.autoCreateSnapshot.bind(templateVersionApi)
export const cleanupSnapshots = templateVersionApi.cleanupSnapshots.bind(templateVersionApi)
export const exportSnapshot = templateVersionApi.exportSnapshot.bind(templateVersionApi)
export const importSnapshot = templateVersionApi.importSnapshot.bind(templateVersionApi)
export const getSnapshotUsage = templateVersionApi.getSnapshotUsage.bind(templateVersionApi)

export default templateVersionApi

// Export utility functions
export function downloadSnapshotFile(blob: Blob, version: string, templateName: string) {
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${templateName}_v${version}_${new Date().toISOString().slice(0, 10)}.json`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

export function formatVersionChanges(changes: VersionComparison['changes']): string {
  const parts: string[] = []
  
  if (changes.template.length > 0) {
    parts.push(`模板变更 ${changes.template.length} 项`)
  }
  
  if (changes.items.length > 0) {
    const added = changes.items.filter(c => c.type === 'added').length
    const removed = changes.items.filter(c => c.type === 'removed').length
    const modified = changes.items.filter(c => c.type === 'modified').length
    
    const itemParts: string[] = []
    if (added > 0) itemParts.push(`新增 ${added} 项`)
    if (removed > 0) itemParts.push(`删除 ${removed} 项`)
    if (modified > 0) itemParts.push(`修改 ${modified} 项`)
    
    if (itemParts.length > 0) {
      parts.push(`检查项变更: ${itemParts.join(', ')}`)
    }
  }
  
  if (changes.config.length > 0) {
    parts.push(`配置变更 ${changes.config.length} 项`)
  }
  
  return parts.length > 0 ? parts.join('; ') : '无变更'
}
